import type { BuildProductAggregatedSalesInfoTableSpecialParams } from "./BuildProductAggregatedSalesInfoTableSpecialParamsTypes"

export const buildProductAggregatedSalesInfoTableSpecialParams: BuildProductAggregatedSalesInfoTableSpecialParams =
  ({ groupBy, groupValue, requestParams }) => {
    return {
      product_asin:
        groupBy === "asin" ? groupValue : requestParams.product_asin,
      marketplace_id:
        groupBy === "marketplace" ? groupValue : requestParams.marketplace_id,
      product_brand:
        groupBy === "brand" ? groupValue : requestParams.product_brand,
      product_manufacturer:
        groupBy === "manufacturer"
          ? groupValue
          : requestParams.product_manufacturer,
      product_type:
        groupBy === "product_type" ? groupValue : requestParams.product_type,
      product_stock_type:
        groupBy === "fulfillment_method"
          ? groupValue.toLowerCase()
          : requestParams.product_stock_type,
    }
  }
