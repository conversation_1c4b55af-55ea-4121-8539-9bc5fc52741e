import l from "utils/intl"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import { NOT_AVAILABLE } from "constants/common"

import type { ProductAggregatedSalesInfoTableInteractiveEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const getCellValue = (
  tableEntryParams: ProductAggregatedSalesInfoTableInteractiveEntryParams,
): string => {
  const { value } = tableEntryParams

  if (checkIsNullOrUndefined(value)) {
    return l(NOT_AVAILABLE)
  }

  return String(value)
}
