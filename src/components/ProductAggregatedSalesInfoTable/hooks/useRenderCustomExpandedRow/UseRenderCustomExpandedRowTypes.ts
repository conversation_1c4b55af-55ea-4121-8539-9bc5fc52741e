import type { ReactNode } from "react"
import type { RenderCustomExpandedRowParams } from "@develop/fe-library"

import type { GetProductAggregatedSalesInfoTableDataParams } from "actions/productAggregatedSalesInfoActions/ProductAggregatedSalesInfoActionsTypes"

import type {
  ProductAggregatedSalesInfoTableData,
  ProductAggregatedSalesInfoTableFilterValues,
} from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

import type { ExpandedRowState } from "types/store/ProductAggregatedSalesInfo"

type UseRenderCustomExpandedRowParams = {
  columnsCount: number
  buildRequestParams: (
    filterValues: Partial<ProductAggregatedSalesInfoTableFilterValues>,
  ) => GetProductAggregatedSalesInfoTableDataParams
  expandedRows: Record<string, ExpandedRowState>
}

type UseRenderCustomExpandedRowReturn = (
  params: RenderCustomExpandedRowParams<
    ProductAggregatedSalesInfoTableData,
    ProductAggregatedSalesInfoTableFilterValues
  >,
) => ReactNode

export type UseRenderCustomExpandedRow = (
  params: UseRenderCustomExpandedRowParams,
) => UseRenderCustomExpandedRowReturn
