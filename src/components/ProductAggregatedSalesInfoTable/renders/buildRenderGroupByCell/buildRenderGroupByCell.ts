import { Option } from "@develop/fe-library"

import { buildGetOptionLabel } from "components/ProductAggregatedSalesInfoTable/utils"

import type { ProductAggregatedSalesInfoTableInteractiveEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const buildRenderGroupByCell =
  (marketplaceOptions: Array<Option>) =>
  (tableEntryParams: ProductAggregatedSalesInfoTableInteractiveEntryParams) => {
    if (tableEntryParams.item.group_by === "marketplace") {
      return buildGetOptionLabel(marketplaceOptions)(tableEntryParams)
    }

    return tableEntryParams.value
  }
