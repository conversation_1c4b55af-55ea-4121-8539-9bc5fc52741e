import React from "react"

import { TableComponent } from "./components"

import { useInitialize } from "./hooks"

import type { ProductAggregatedSalesInfoTableProps } from "./ProductAggregatedSalesInfoTableTypes"

export const ProductAggregatedSalesInfoTable = ({
  urlSearchDefaultValue,
}: ProductAggregatedSalesInfoTableProps) => {
  const { isInitialized } = useInitialize()

  if (!isInitialized) {
    return null
  }

  return <TableComponent urlSearchDefaultValue={urlSearchDefaultValue} />
}
