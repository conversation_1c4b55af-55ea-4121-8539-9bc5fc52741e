import React from "react"
import { TextLink } from "@develop/fe-library"

import l from "utils/intl"
import { checkIsNullOrUndefined } from "utils/validationHelper"

import { NOT_AVAILABLE } from "constants/common"

import { getOrdersLink } from "../../utils"

import type { OrdersLinkCellProps } from "./OrdersLinkCellTypes"

export const OrdersLinkCell = ({
  tableEntryParams,
  ordersUrlParams,
}: OrdersLinkCellProps) => {
  const { value, item } = tableEntryParams

  if (checkIsNullOrUndefined(value)) {
    return <>{l(NOT_AVAILABLE)}</>
  }

  const link = getOrdersLink({
    seller_id: item.seller_id,
    marketplace_id: item.marketplace_id,
    seller_sku: item.seller_sku,
    ordersUrlParams,
  })

  if (!link) {
    return <>{String(value)}</>
  }

  return (
    <TextLink
      href={link}
      rel="noopener noreferrer"
      target="_blank"
      typographyProps={{
        variant: "--font-body-text-9",
      }}
    >
      {String(value)}
    </TextLink>
  )
}
