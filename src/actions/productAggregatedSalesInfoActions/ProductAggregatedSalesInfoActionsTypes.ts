import type { Dispatch } from "redux"

import type { ActionAsyncDefinitionType, ActionsTypes } from "actions/types"

import { AsyncStatus } from "types"
import { ProductAggregatedSalesInfoTableGroupBy } from "types/ProductAggregatedSalesInfoTableGroupBy"
import type { GetProductAggregatedSalesInfosRequestParams } from "types/RequestParams/GetProductAggregatedSalesInfosRequestParams"
import type { ProductAggregatedSalesInfoNormalized } from "types/store/ProductAggregatedSalesInfo"
import { ExpandedRowState } from "types/store/ProductAggregatedSalesInfo"
import type { RootState } from "types/store/store"
import type {
  TableSettingsActionDefinitions,
  TableSettingsActionTypes,
} from "types/TableSettings"

export type ProductAggregatedSalesInfoActionsAsyncTypeNames =
  | "getProductAggregatedSalesInfoTableData"
  | "expandRow"

export type ProductAggregatedSalesInfoActionsSyncTypeNames =
  | "changeExpandedRowState"
  | "clearExpandedRowState"
  | "resetExpandedRowStates"
  | "collapseRow"
  | "collapseAllRows"

export type ProductAggregatedSalesInfoTypes = ActionsTypes<
  ProductAggregatedSalesInfoActionsAsyncTypeNames,
  ProductAggregatedSalesInfoActionsSyncTypeNames
> &
  TableSettingsActionTypes

export type GetProductAggregatedSalesInfoTableDataParams = {
  groupBy?: ProductAggregatedSalesInfoTableGroupBy
  requestParams: Omit<GetProductAggregatedSalesInfosRequestParams, "offer_type">
}

type ChangeExpandedRowStatusParams = {
  id: string
  status: AsyncStatus
  error: null | any
  loadedCount?: number
}

export type ProductAggregatedSalesInfoDefinitions = {
  getProductAggregatedSalesInfoTableData: ActionAsyncDefinitionType<GetProductAggregatedSalesInfoTableDataParams>
  changeExpandedRowState: (
    params: ChangeExpandedRowStatusParams,
    dispatch?: Dispatch,
    getState?: () => RootState,
  ) => Record<string, ExpandedRowState>
  clearExpandedRowState: (
    id: string,
    dispatch?: Dispatch,
    getState?: () => RootState,
  ) => Record<string, ExpandedRowState>
  resetExpandedRowStates: (
    expandedRowIds: Set<string>,
    dispatch?: Dispatch,
    getState?: () => RootState,
  ) => Record<string, ExpandedRowState>
  expandRow: ActionAsyncDefinitionType<{
    id: string
    page: number
    requestParams: GetProductAggregatedSalesInfosRequestParams
  }>
  collapseRow: (
    id: string,
    dispatch?: Dispatch,
    getState?: () => RootState,
  ) => Array<ProductAggregatedSalesInfoNormalized>
  collapseAllRows: (
    dispatch?: Dispatch,
    getState?: () => RootState,
  ) => Array<ProductAggregatedSalesInfoNormalized>
} & TableSettingsActionDefinitions
