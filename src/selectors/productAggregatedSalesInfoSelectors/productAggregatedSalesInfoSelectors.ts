import { createSelector } from "reselect"

import { userGroupedMarketplaceSelector } from "selectors/marketplaceSelectors"

import { ASYNC_STATUSES } from "constants/async"

import type { ProductAggregatedSalesInfoState } from "types/store/ProductAggregatedSalesInfo"
import type { RootState } from "types/store/store"
import type { TableSettings } from "types/TableSettings"

export const productAggregatedSalesInfoSelector = (state: RootState) => {
  return state.productAggregatedSalesInfo
}

// @ts-expect-error
export const productAggregatedSalesInfoTableSettingsSelector = createSelector(
  [productAggregatedSalesInfoSelector, userGroupedMarketplaceSelector],
  (state: ProductAggregatedSalesInfoState, { basAccounts }): TableSettings => {
    const { tableSettings } = state

    const settings = tableSettings.settings.map((setting) => {
      if (setting.name === "seller_id") {
        return {
          ...setting,
          value: basAccounts.length > 1,
        }
      }

      return setting
    })

    return {
      ...tableSettings,
      settings,
    } as TableSettings
  },
)

export const productAggregatedSalesInfoTableStatesSelector = createSelector(
  productAggregatedSalesInfoSelector,
  (state: ProductAggregatedSalesInfoState) => {
    const isLoading =
      state.productAggregatedSalesInfoGrouped?.status === ASYNC_STATUSES.PENDING

    if (!state.productAggregatedSalesInfoGrouped?.data) {
      return {
        data: [],
        totalCount: 0,
        isLoading,
        expandedRows: {},
      }
    }

    const { data, totalCount } =
      state.productAggregatedSalesInfoGrouped.data || {}

    return {
      data,
      totalCount,
      isLoading,
      expandedRows: state.productAggregatedSalesInfoGrouped.expandedRows,
    }
  },
)
