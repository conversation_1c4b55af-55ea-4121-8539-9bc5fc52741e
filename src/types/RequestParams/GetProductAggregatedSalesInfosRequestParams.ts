import { JSONRequestParam } from "./global"

export type GetProductAggregatedSalesInfosRequestParams = {
  customerId?: string
  page?: number
  sort?: string
  pageSize?: number
  id?: number
  // group_by?: string
  date_start?: string
  date_end?: string
  marketplace_seller_ids?: JSONRequestParam
  currency_id?: string
  product_id?: number
  marketplace_id?: string
  seller_id?: string
  seller_sku?: string
  product_asin?: string
  product_brand?: string
  product_ean?: string
  product_upc?: string
  product_isbn?: string
  product_title?: string
  product_manufacturer?: string
  product_type?: string
  product_parent_asin?: string
  product_stock_type?: string
  product_adult?: "1" | "0"
  tag_id?: string
  estimated_profit_amount?: string
  refunds?: string
  markup?: string
  roi?: string
  margin?: string
  amazon_fees?: string
  // expenses_amount_without_fees?: string
  revenue_amount?: string
  ppc_costs?: string
  units?: string
  orders?: string
  is_transaction_date_mode?: "1" | "0"
  total_income?: string
  expenses_amount?: string
  offer_type?: string // Not present in grouped
}
