import type { GetProductAggregatedSalesInfoGroupedRequestParams } from "types/RequestParams/GetProductAggregatedSalesInfoGroupedRequestParams"
import type { GetProductAggregatedSalesInfosRequestParams } from "types/RequestParams/GetProductAggregatedSalesInfosRequestParams"

export type GetProductAggregatedSalesInfoArgs = {
  params: GetProductAggregatedSalesInfosRequestParams
  customerId: number
}

export type GetProductAggregatedSalesInfoGroupedArgs = {
  params: GetProductAggregatedSalesInfoGroupedRequestParams
  customerId: number
}
